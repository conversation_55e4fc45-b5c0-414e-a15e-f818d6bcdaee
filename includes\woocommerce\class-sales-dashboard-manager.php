<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Sales Dashboard Manager
 *
 * Creates comprehensive sales dashboards using DAB dashboard builder
 */
class DAB_Sales_Dashboard_Manager {

    /**
     * Initialize the Sales Dashboard Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Add dashboard widgets to WordPress admin
        add_action('wp_dashboard_setup', array(__CLASS__, 'add_dashboard_widgets'));

        // Register dashboard shortcodes
        add_shortcode('dab_sales_overview', array(__CLASS__, 'render_sales_overview_shortcode'));
        add_shortcode('dab_sales_chart', array(__CLASS__, 'render_sales_chart_shortcode'));
        add_shortcode('dab_top_products', array(__CLASS__, 'render_top_products_shortcode'));
        add_shortcode('dab_recent_orders', array(__CLASS__, 'render_recent_orders_shortcode'));

        // Handle AJAX requests
        add_action('wp_ajax_dab_get_sales_data', array(__CLASS__, 'ajax_get_sales_data'));
        add_action('wp_ajax_dab_get_sales_chart_data', array(__CLASS__, 'ajax_get_sales_chart_data'));
        add_action('wp_ajax_dab_export_sales_report', array(__CLASS__, 'ajax_export_sales_report'));

        // Enqueue dashboard scripts and styles
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_dashboard_assets'));

        // Use a safer approach for frontend assets with class existence check
        add_action('wp_enqueue_scripts', function() {
            if (class_exists('DAB_Sales_Dashboard_Manager') && method_exists('DAB_Sales_Dashboard_Manager', 'enqueue_frontend_assets')) {
                DAB_Sales_Dashboard_Manager::enqueue_frontend_assets();
            }
        });
    }

    /**
     * Create database tables for sales dashboard
     */
    public static function create_tables() {
        global $wpdb;

        // Sales analytics cache table
        $table_name = $wpdb->prefix . 'dab_wc_sales_analytics';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            metric_name varchar(255) NOT NULL,
            metric_value longtext,
            metric_date date NOT NULL,
            metric_period varchar(20) DEFAULT 'daily',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY metric_date_period (metric_name, metric_date, metric_period),
            KEY metric_name (metric_name),
            KEY metric_date (metric_date)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Sales dashboard configurations table
        $dashboard_table = $wpdb->prefix . 'dab_wc_sales_dashboards';

        $sql_dashboard = "CREATE TABLE $dashboard_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            dashboard_name varchar(255) NOT NULL,
            dashboard_config longtext,
            dashboard_type varchar(50) DEFAULT 'sales',
            is_active tinyint(1) DEFAULT 1,
            user_roles text,
            created_by bigint(20),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY dashboard_name (dashboard_name)
        ) $charset_collate;";

        dbDelta($sql_dashboard);

        // Create default sales dashboard
        self::create_default_sales_dashboard();
    }

    /**
     * Create default sales dashboard
     */
    public static function create_default_sales_dashboard() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_sales_dashboards';

        $default_config = array(
            'widgets' => array(
                array(
                    'type' => 'sales_overview',
                    'title' => __('Sales Overview', 'db-app-builder'),
                    'position' => array('row' => 1, 'col' => 1),
                    'size' => array('width' => 12, 'height' => 4)
                ),
                array(
                    'type' => 'sales_chart',
                    'title' => __('Sales Chart', 'db-app-builder'),
                    'position' => array('row' => 2, 'col' => 1),
                    'size' => array('width' => 8, 'height' => 6),
                    'config' => array('period' => '30days', 'chart_type' => 'line')
                ),
                array(
                    'type' => 'top_products',
                    'title' => __('Top Products', 'db-app-builder'),
                    'position' => array('row' => 2, 'col' => 9),
                    'size' => array('width' => 4, 'height' => 6),
                    'config' => array('limit' => 10)
                ),
                array(
                    'type' => 'recent_orders',
                    'title' => __('Recent Orders', 'db-app-builder'),
                    'position' => array('row' => 3, 'col' => 1),
                    'size' => array('width' => 12, 'height' => 6),
                    'config' => array('limit' => 20)
                )
            ),
            'refresh_interval' => 300, // 5 minutes
            'date_range' => '30days'
        );

        $existing = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM $table_name WHERE dashboard_name = %s",
                'Default Sales Dashboard'
            )
        );

        if (!$existing) {
            $wpdb->insert(
                $table_name,
                array(
                    'dashboard_name' => 'Default Sales Dashboard',
                    'dashboard_config' => serialize($default_config),
                    'dashboard_type' => 'sales',
                    'is_active' => 1,
                    'user_roles' => serialize(array('administrator', 'shop_manager')),
                    'created_by' => get_current_user_id()
                )
            );
        }
    }

    /**
     * Add dashboard widgets to WordPress admin
     */
    public static function add_dashboard_widgets() {
        if (!current_user_can('manage_woocommerce')) {
            return;
        }

        wp_add_dashboard_widget(
            'dab_sales_overview_widget',
            __('Sales Overview (DAB)', 'db-app-builder'),
            array(__CLASS__, 'render_sales_overview_widget')
        );

        wp_add_dashboard_widget(
            'dab_top_products_widget',
            __('Top Products (DAB)', 'db-app-builder'),
            array(__CLASS__, 'render_top_products_widget')
        );
    }

    /**
     * Render sales overview widget
     */
    public static function render_sales_overview_widget() {
        $sales_data = self::get_sales_overview_data();

        echo '<div class="dab-sales-overview-widget">';
        echo '<div class="dab-sales-metrics">';

        foreach ($sales_data as $metric => $data) {
            echo '<div class="dab-metric">';
            echo '<div class="dab-metric-value">' . esc_html($data['value']) . '</div>';
            echo '<div class="dab-metric-label">' . esc_html($data['label']) . '</div>';
            if (isset($data['change'])) {
                $change_class = $data['change'] >= 0 ? 'positive' : 'negative';
                echo '<div class="dab-metric-change ' . $change_class . '">' . esc_html($data['change']) . '%</div>';
            }
            echo '</div>';
        }

        echo '</div>';
        echo '<p><a href="' . admin_url('admin.php?page=dab_woocommerce_sales_dashboard') . '">' . __('View Full Dashboard', 'db-app-builder') . '</a></p>';
        echo '</div>';

        // Add CSS for widget styling
        echo '<style>
        .dab-sales-overview-widget .dab-sales-metrics {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }
        .dab-sales-overview-widget .dab-metric {
            flex: 1;
            min-width: 120px;
            text-align: center;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .dab-sales-overview-widget .dab-metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #2271b1;
        }
        .dab-sales-overview-widget .dab-metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .dab-sales-overview-widget .dab-metric-change {
            font-size: 11px;
            margin-top: 3px;
        }
        .dab-sales-overview-widget .dab-metric-change.positive {
            color: #00a32a;
        }
        .dab-sales-overview-widget .dab-metric-change.negative {
            color: #d63638;
        }
        </style>';
    }

    /**
     * Get sales overview data
     */
    public static function get_sales_overview_data() {
        $current_period = self::get_sales_data_for_period('today');
        $previous_period = self::get_sales_data_for_period('yesterday');

        $data = array(
            'total_sales' => array(
                'value' => wc_price($current_period['total_sales']),
                'label' => __('Today\'s Sales', 'db-app-builder'),
                'change' => self::calculate_percentage_change($current_period['total_sales'], $previous_period['total_sales'])
            ),
            'total_orders' => array(
                'value' => number_format($current_period['total_orders']),
                'label' => __('Today\'s Orders', 'db-app-builder'),
                'change' => self::calculate_percentage_change($current_period['total_orders'], $previous_period['total_orders'])
            ),
            'average_order' => array(
                'value' => wc_price($current_period['average_order_value']),
                'label' => __('Avg Order Value', 'db-app-builder'),
                'change' => self::calculate_percentage_change($current_period['average_order_value'], $previous_period['average_order_value'])
            )
        );

        return $data;
    }

    /**
     * Render top products widget
     */
    public static function render_top_products_widget() {
        if (!class_exists('WooCommerce')) {
            echo '<p>' . __('WooCommerce is not active.', 'db-app-builder') . '</p>';
            return;
        }

        // Get top products data
        global $wpdb;
        $top_products = $wpdb->get_results("
            SELECT p.post_title as product_name,
                   SUM(CAST(oim.meta_value AS UNSIGNED)) as quantity_sold,
                   SUM(CAST(oim.meta_value AS UNSIGNED) * CAST(oim2.meta_value AS DECIMAL(10,2))) as total_sales
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON p.ID = oim.meta_value
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim2 ON oim.order_item_id = oim2.order_item_id
            INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON oim.order_item_id = oi.order_item_id
            INNER JOIN {$wpdb->posts} orders ON oi.order_id = orders.ID
            WHERE oim.meta_key = '_product_id'
            AND oim2.meta_key = '_qty'
            AND orders.post_status IN ('wc-completed', 'wc-processing')
            AND orders.post_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            GROUP BY p.ID
            ORDER BY quantity_sold DESC
            LIMIT 5
        ");

        echo '<div class="dab-top-products-widget">';

        if (empty($top_products)) {
            echo '<p>' . __('No product sales data available.', 'db-app-builder') . '</p>';
        } else {
            echo '<div class="dab-top-products-list">';
            foreach ($top_products as $index => $product) {
                echo '<div class="dab-product-item">';
                echo '<div class="dab-product-rank">' . ($index + 1) . '</div>';
                echo '<div class="dab-product-info">';
                echo '<div class="dab-product-name">' . esc_html($product->product_name) . '</div>';
                echo '<div class="dab-product-stats">';
                echo number_format($product->quantity_sold) . ' ' . __('sold', 'db-app-builder');
                if (function_exists('wc_price')) {
                    echo ' • ' . wc_price($product->total_sales);
                } else {
                    echo ' • $' . number_format($product->total_sales, 2);
                }
                echo '</div>';
                echo '</div>';
                echo '</div>';
            }
            echo '</div>';
        }

        echo '<p><a href="' . admin_url('admin.php?page=dab_woocommerce_sales_dashboard') . '">' . __('View Full Dashboard', 'db-app-builder') . '</a></p>';
        echo '</div>';

        // Add CSS for widget styling
        echo '<style>
        .dab-top-products-widget .dab-top-products-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .dab-top-products-widget .dab-product-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .dab-top-products-widget .dab-product-rank {
            width: 24px;
            height: 24px;
            background: #2271b1;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            margin-right: 10px;
        }
        .dab-top-products-widget .dab-product-info {
            flex: 1;
        }
        .dab-top-products-widget .dab-product-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .dab-top-products-widget .dab-product-stats {
            font-size: 12px;
            color: #666;
        }
        </style>';
    }

    /**
     * Get sales data for a specific period
     */
    public static function get_sales_data_for_period($period) {
        global $wpdb;

        $date_query = self::get_date_query_for_period($period);

        $orders_table = $wpdb->prefix . 'posts';
        $order_meta_table = $wpdb->prefix . 'postmeta';

        // Get total sales and order count
        $sales_query = "
            SELECT
                COUNT(p.ID) as total_orders,
                SUM(CAST(pm.meta_value AS DECIMAL(10,2))) as total_sales
            FROM {$orders_table} p
            LEFT JOIN {$order_meta_table} pm ON p.ID = pm.post_id AND pm.meta_key = '_order_total'
            WHERE p.post_type = 'shop_order'
            AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
            {$date_query}
        ";

        $results = $wpdb->get_row($sales_query);

        $total_orders = $results->total_orders ?: 0;
        $total_sales = $results->total_sales ?: 0;
        $average_order_value = $total_orders > 0 ? ($total_sales / $total_orders) : 0;

        return array(
            'total_orders' => $total_orders,
            'total_sales' => $total_sales,
            'average_order_value' => $average_order_value
        );
    }

    /**
     * Get date query for period
     */
    public static function get_date_query_for_period($period) {
        switch ($period) {
            case 'today':
                return "AND DATE(p.post_date) = CURDATE()";
            case 'yesterday':
                return "AND DATE(p.post_date) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
            case 'this_week':
                return "AND YEARWEEK(p.post_date, 1) = YEARWEEK(CURDATE(), 1)";
            case 'last_week':
                return "AND YEARWEEK(p.post_date, 1) = YEARWEEK(DATE_SUB(CURDATE(), INTERVAL 1 WEEK), 1)";
            case 'this_month':
                return "AND YEAR(p.post_date) = YEAR(CURDATE()) AND MONTH(p.post_date) = MONTH(CURDATE())";
            case 'last_month':
                return "AND YEAR(p.post_date) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) AND MONTH(p.post_date) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))";
            case '7days':
                return "AND p.post_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)";
            case '30days':
                return "AND p.post_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
            default:
                return "";
        }
    }

    /**
     * Calculate percentage change
     */
    public static function calculate_percentage_change($current, $previous) {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    /**
     * Get date query for period (alias for get_date_query_for_period)
     */
    public static function get_date_query($period) {
        return self::get_date_query_for_period($period);
    }

    /**
     * Enqueue dashboard assets
     */
    public static function enqueue_dashboard_assets($hook) {
        $hook_string = is_null($hook) ? '' : (string)$hook;
        if ($hook_string === '' || strpos($hook_string, 'dab_woocommerce_sales_dashboard') === false) {
            return;
        }

        wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', array(), '3.9.1', true);
        wp_enqueue_script('dab-sales-dashboard', plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/sales-dashboard.js', array('jquery', 'chart-js'), DAB_VERSION, true);

        wp_localize_script('dab-sales-dashboard', 'dabSalesDashboard', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_sales_dashboard_nonce'),
            'i18n' => array(
                'loading' => __('Loading...', 'db-app-builder'),
                'error' => __('Error loading data', 'db-app-builder'),
                'noData' => __('No data available', 'db-app-builder')
            )
        ));

        wp_enqueue_style('dab-sales-dashboard', plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/sales-dashboard.css', array(), DAB_VERSION);
    }

    /**
     * Enqueue frontend assets for sales dashboard shortcodes
     */
    public static function enqueue_frontend_assets() {
        // Only enqueue on pages that have sales dashboard shortcodes
        if (!self::should_load_frontend_assets()) {
            return;
        }

        // Enqueue Chart.js for frontend charts
        wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', array(), '3.9.1', true);

        // Enqueue frontend sales dashboard script
        wp_enqueue_script(
            'dab-sales-dashboard-frontend',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/sales-dashboard-frontend.js',
            array('jquery', 'chart-js'),
            DAB_VERSION,
            true
        );

        // Localize script for AJAX
        wp_localize_script('dab-sales-dashboard-frontend', 'dabSalesDashboardFrontend', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_sales_dashboard_nonce'),
            'i18n' => array(
                'loading' => __('Loading...', 'db-app-builder'),
                'error' => __('Error loading data', 'db-app-builder'),
                'noData' => __('No data available', 'db-app-builder'),
                'currency' => function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$',
                'dateFormat' => get_option('date_format')
            )
        ));

        // Enqueue frontend styles
        wp_enqueue_style(
            'dab-sales-dashboard-frontend',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/sales-dashboard-frontend.css',
            array(),
            DAB_VERSION
        );
    }

    /**
     * Check if frontend assets should be loaded
     */
    private static function should_load_frontend_assets() {
        global $post;

        // Check if current page has sales dashboard shortcodes
        if ($post && is_object($post)) {
            $shortcodes = array(
                'dab_sales_overview',
                'dab_sales_chart',
                'dab_top_products',
                'dab_recent_orders'
            );

            foreach ($shortcodes as $shortcode) {
                if (has_shortcode($post->post_content, $shortcode)) {
                    return true;
                }
            }
        }

        // Check if we're on a WooCommerce page that might need sales data
        if (function_exists('is_woocommerce') && (is_woocommerce() || is_cart() || is_checkout() || is_account_page())) {
            return true;
        }

        // Check if we're on a page with DAB sales widgets
        if (is_active_widget(false, false, 'dab_sales_widget')) {
            return true;
        }

        return false;
    }

    /**
     * AJAX handler for getting sales data (frontend)
     */
    public static function ajax_get_sales_data() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'dab_sales_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            wp_send_json_error('WooCommerce is not active');
            return;
        }

        $period = sanitize_text_field($_POST['period'] ?? 'today');
        $metric = sanitize_text_field($_POST['metric'] ?? 'revenue');

        try {
            $data = array();

            switch ($metric) {
                case 'revenue':
                    $data = self::get_revenue_data($period);
                    break;
                case 'orders':
                    $data = self::get_orders_data($period);
                    break;
                case 'customers':
                    $data = self::get_customers_data($period);
                    break;
                case 'products':
                    $data = self::get_products_data($period);
                    break;
                default:
                    $data = self::get_revenue_data($period);
            }

            wp_send_json_success($data);
        } catch (Exception $e) {
            wp_send_json_error('Failed to get sales data: ' . $e->getMessage());
        }
    }

    /**
     * AJAX handler for getting sales chart data (frontend)
     */
    public static function ajax_get_sales_chart_data() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'dab_sales_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            wp_send_json_error('WooCommerce is not active');
            return;
        }

        $chart_type = sanitize_text_field($_POST['chart_type'] ?? 'line');
        $period = sanitize_text_field($_POST['period'] ?? '7days');
        $metric = sanitize_text_field($_POST['metric'] ?? 'revenue');

        try {
            $chart_data = self::get_chart_data($chart_type, $period, $metric);
            wp_send_json_success($chart_data);
        } catch (Exception $e) {
            wp_send_json_error('Failed to get chart data: ' . $e->getMessage());
        }
    }

    /**
     * AJAX handler for exporting sales report (frontend)
     */
    public static function ajax_export_sales_report() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'dab_sales_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check permissions
        if (!current_user_can('view_woocommerce_reports')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            wp_send_json_error('WooCommerce is not active');
            return;
        }

        $period = sanitize_text_field($_POST['period'] ?? '30days');
        $format = sanitize_text_field($_POST['format'] ?? 'csv');

        try {
            $export_data = self::generate_sales_report($period, $format);
            wp_send_json_success($export_data);
        } catch (Exception $e) {
            wp_send_json_error('Failed to export report: ' . $e->getMessage());
        }
    }

    /**
     * Get revenue data for specified period
     */
    private static function get_revenue_data($period) {
        global $wpdb;

        $date_query = self::get_date_query($period);

        $revenue = $wpdb->get_var("
            SELECT SUM(meta_value)
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_order_total'
            AND p.post_type = 'shop_order'
            AND p.post_status IN ('wc-completed', 'wc-processing')
            {$date_query}
        ");

        return array(
            'value' => floatval($revenue),
            'formatted' => function_exists('wc_price') ? wc_price($revenue) : '$' . number_format($revenue, 2),
            'period' => $period
        );
    }

    /**
     * Get orders data for specified period
     */
    private static function get_orders_data($period) {
        global $wpdb;

        $date_query = self::get_date_query($period);

        $orders = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'shop_order'
            AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-pending')
            {$date_query}
        ");

        return array(
            'value' => intval($orders),
            'period' => $period
        );
    }

    /**
     * Get customers data for specified period
     */
    private static function get_customers_data($period) {
        global $wpdb;

        $date_query = self::get_date_query($period);

        $customers = $wpdb->get_var("
            SELECT COUNT(DISTINCT pm.meta_value)
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_customer_user'
            AND pm.meta_value > 0
            AND p.post_type = 'shop_order'
            AND p.post_status IN ('wc-completed', 'wc-processing')
            {$date_query}
        ");

        return array(
            'value' => intval($customers),
            'period' => $period
        );
    }

    /**
     * Get products data for specified period
     */
    private static function get_products_data($period) {
        global $wpdb;

        $date_query = self::get_date_query($period);

        $products = $wpdb->get_var("
            SELECT COUNT(DISTINCT oi.product_id)
            FROM {$wpdb->prefix}woocommerce_order_items oi
            INNER JOIN {$wpdb->posts} p ON oi.order_id = p.ID
            WHERE oi.order_item_type = 'line_item'
            AND p.post_type = 'shop_order'
            AND p.post_status IN ('wc-completed', 'wc-processing')
            {$date_query}
        ");

        return array(
            'value' => intval($products),
            'period' => $period
        );
    }

    /**
     * Get chart data for visualization
     */
    private static function get_chart_data($chart_type, $period, $metric) {
        // Implementation would depend on the specific chart requirements
        // This is a basic structure
        return array(
            'type' => $chart_type,
            'labels' => array(),
            'datasets' => array(),
            'options' => array()
        );
    }

    /**
     * Generate sales report for export
     */
    private static function generate_sales_report($period, $format) {
        // Implementation for generating exportable reports
        return array(
            'filename' => 'sales-report-' . $period . '-' . date('Y-m-d') . '.' . $format,
            'data' => array(),
            'format' => $format
        );
    }

    /**
     * Render sales overview shortcode
     */
    public static function render_sales_overview_shortcode($atts) {
        $atts = shortcode_atts(array(
            'period' => 'today',
            'show_change' => 'true'
        ), $atts, 'dab_sales_overview');

        if (!class_exists('WooCommerce')) {
            return '<p>' . __('WooCommerce is not active.', 'db-app-builder') . '</p>';
        }

        $sales_data = self::get_sales_overview_data();

        ob_start();
        ?>
        <div class="dab-sales-overview-shortcode">
            <div class="dab-sales-metrics">
                <?php foreach ($sales_data as $metric => $data): ?>
                    <div class="dab-metric">
                        <div class="dab-metric-value"><?php echo esc_html($data['value']); ?></div>
                        <div class="dab-metric-label"><?php echo esc_html($data['label']); ?></div>
                        <?php if ($atts['show_change'] === 'true' && isset($data['change'])): ?>
                            <div class="dab-metric-change <?php echo $data['change'] >= 0 ? 'positive' : 'negative'; ?>">
                                <?php echo esc_html($data['change']); ?>%
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render sales chart shortcode
     */
    public static function render_sales_chart_shortcode($atts) {
        $atts = shortcode_atts(array(
            'type' => 'line',
            'period' => '7days',
            'height' => '400'
        ), $atts, 'dab_sales_chart');

        if (!class_exists('WooCommerce')) {
            return '<p>' . __('WooCommerce is not active.', 'db-app-builder') . '</p>';
        }

        $chart_id = 'dab-sales-chart-' . uniqid();

        ob_start();
        ?>
        <div class="dab-sales-chart-shortcode">
            <canvas id="<?php echo esc_attr($chart_id); ?>" style="height: <?php echo esc_attr($atts['height']); ?>px;"></canvas>
        </div>
        <script>
        jQuery(document).ready(function($) {
            // Chart implementation would go here
            console.log('Sales chart shortcode loaded');
        });
        </script>
        <?php
        return ob_get_clean();
    }

    /**
     * Render top products shortcode
     */
    public static function render_top_products_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => '5',
            'period' => '30days'
        ), $atts, 'dab_top_products');

        if (!class_exists('WooCommerce')) {
            return '<p>' . __('WooCommerce is not active.', 'db-app-builder') . '</p>';
        }

        global $wpdb;
        $limit = intval($atts['limit']);

        $top_products = $wpdb->get_results($wpdb->prepare("
            SELECT p.post_title as product_name,
                   SUM(CAST(oim.meta_value AS UNSIGNED)) as quantity_sold,
                   SUM(CAST(oim.meta_value AS UNSIGNED) * CAST(oim2.meta_value AS DECIMAL(10,2))) as total_sales
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON p.ID = oim.meta_value
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim2 ON oim.order_item_id = oim2.order_item_id
            INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON oim.order_item_id = oi.order_item_id
            INNER JOIN {$wpdb->posts} orders ON oi.order_id = orders.ID
            WHERE oim.meta_key = '_product_id'
            AND oim2.meta_key = '_qty'
            AND orders.post_status IN ('wc-completed', 'wc-processing')
            AND orders.post_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            GROUP BY p.ID
            ORDER BY quantity_sold DESC
            LIMIT %d
        ", $limit));

        ob_start();
        ?>
        <div class="dab-top-products-shortcode">
            <?php if (empty($top_products)): ?>
                <p><?php _e('No product sales data available.', 'db-app-builder'); ?></p>
            <?php else: ?>
                <div class="dab-top-products-list">
                    <?php foreach ($top_products as $index => $product): ?>
                        <div class="dab-product-item">
                            <div class="dab-product-rank"><?php echo $index + 1; ?></div>
                            <div class="dab-product-info">
                                <div class="dab-product-name"><?php echo esc_html($product->product_name); ?></div>
                                <div class="dab-product-stats">
                                    <?php echo number_format($product->quantity_sold); ?> <?php _e('sold', 'db-app-builder'); ?>
                                    <?php if (function_exists('wc_price')): ?>
                                        • <?php echo wc_price($product->total_sales); ?>
                                    <?php else: ?>
                                        • $<?php echo number_format($product->total_sales, 2); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render recent orders shortcode
     */
    public static function render_recent_orders_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => '10',
            'status' => 'any'
        ), $atts, 'dab_recent_orders');

        if (!class_exists('WooCommerce')) {
            return '<p>' . __('WooCommerce is not active.', 'db-app-builder') . '</p>';
        }

        $limit = intval($atts['limit']);
        $status = $atts['status'] === 'any' ? array('wc-completed', 'wc-processing', 'wc-pending', 'wc-on-hold') : array($atts['status']);

        $orders = wc_get_orders(array(
            'limit' => $limit,
            'status' => $status,
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        ob_start();
        ?>
        <div class="dab-recent-orders-shortcode">
            <?php if (empty($orders)): ?>
                <p><?php _e('No recent orders found.', 'db-app-builder'); ?></p>
            <?php else: ?>
                <div class="dab-recent-orders-list">
                    <?php foreach ($orders as $order): ?>
                        <div class="dab-order-item">
                            <div class="dab-order-number">#<?php echo $order->get_order_number(); ?></div>
                            <div class="dab-order-info">
                                <div class="dab-order-customer"><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></div>
                                <div class="dab-order-details">
                                    <?php echo wc_price($order->get_total()); ?> •
                                    <span class="dab-order-status status-<?php echo esc_attr($order->get_status()); ?>">
                                        <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                                    </span> •
                                    <?php echo esc_html($order->get_date_created()->date_i18n(get_option('date_format'))); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }

}
